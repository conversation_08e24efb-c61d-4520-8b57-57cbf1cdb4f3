from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from pydantic import BaseModel
from typing import List, Optional
import os
import uuid
import asyncio
from pathlib import Path

from services.model_manager import ModelManager
from services.image_generator import ImageGenerator
from services.sprite_sheet_generator import SpriteSheetGenerator

app = FastAPI(title="像素游戏图像生成器", version="1.0.0")

# CORS设置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 静态文件服务
os.makedirs("outputs", exist_ok=True)
app.mount("/outputs", StaticFiles(directory="outputs"), name="outputs")

# 前端静态文件服务
frontend_build_path = Path("../frontend/build")
if frontend_build_path.exists():
    app.mount("/static", StaticFiles(directory="../frontend/build/static"), name="static")

    @app.get("/", response_class=FileResponse)
    async def serve_frontend():
        return "../frontend/build/index.html"

    @app.get("/{path:path}", response_class=FileResponse)
    async def serve_frontend_routes(path: str):
        file_path = Path(f"../frontend/build/{path}")
        if file_path.exists() and file_path.is_file():
            return str(file_path)
        else:
            return "../frontend/build/index.html"

# 初始化服务
model_manager = ModelManager()
image_generator = ImageGenerator()
sprite_sheet_generator = SpriteSheetGenerator()

class GenerateRequest(BaseModel):
    prompt: str
    model: str
    width: int = 512
    height: int = 512
    steps: int = 20
    cfg_scale: float = 7.0
    batch_size: int = 1
    seed: int = -1

class SpriteSheetRequest(BaseModel):
    images: List[str]
    settings: dict

@app.on_event("startup")
async def startup_event():
    """应用启动时初始化模型"""
    await model_manager.initialize()
    print("🚀 像素游戏图像生成器启动完成!")

@app.get("/")
async def root():
    return {"message": "像素游戏图像生成器 API", "status": "running"}

@app.get("/api/models")
async def get_models():
    """获取可用的模型列表"""
    try:
        models = await model_manager.get_available_models()
        return {"models": models}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取模型列表失败: {str(e)}")

@app.post("/api/generate")
async def generate_images(request: GenerateRequest):
    """生成图像"""
    try:
        # 验证模型是否存在
        if not await model_manager.is_model_available(request.model):
            raise HTTPException(status_code=400, detail=f"模型 {request.model} 不可用")
        
        # 生成图像
        images = await image_generator.generate(
            prompt=request.prompt,
            model=request.model,
            width=request.width,
            height=request.height,
            steps=request.steps,
            cfg_scale=request.cfg_scale,
            batch_size=request.batch_size,
            seed=request.seed
        )
        
        return {"images": images, "count": len(images)}
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"图像生成失败: {str(e)}")

@app.post("/api/generate-sprite-sheet")
async def generate_sprite_sheet(request: SpriteSheetRequest):
    """生成雪碧图"""
    try:
        sprite_sheet_url = await sprite_sheet_generator.create_sprite_sheet(
            image_urls=request.images,
            settings=request.settings
        )
        
        return {"sprite_sheet_url": sprite_sheet_url}
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"雪碧图生成失败: {str(e)}")

@app.get("/api/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "models_loaded": len(await model_manager.get_loaded_models()),
        "gpu_available": image_generator.is_gpu_available()
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
