import React, { useState } from 'react';
import styled from 'styled-components';

const GeneratorContainer = styled.div`
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
`;

const Title = styled.h3`
  margin: 0 0 20px 0;
  color: #333;
  font-size: 1.3rem;
  display: flex;
  align-items: center;
  gap: 10px;
`;

const FormGroup = styled.div`
  margin-bottom: 20px;
`;

const Label = styled.label`
  display: block;
  margin-bottom: 8px;
  color: #555;
  font-weight: 500;
`;

const TextArea = styled.textarea`
  width: 100%;
  min-height: 120px;
  padding: 15px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 1rem;
  resize: vertical;
  font-family: inherit;
  transition: all 0.3s ease;
  
  &:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  }
  
  &::placeholder {
    color: #999;
  }
`;

const SettingsGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-bottom: 20px;
`;

const Input = styled.input`
  width: 100%;
  padding: 10px 12px;
  border: 2px solid #e1e5e9;
  border-radius: 6px;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  
  &:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  }
`;

const GenerateButton = styled.button`
  width: 100%;
  padding: 15px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  
  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
  }
  
  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
  }
  
  &:active {
    transform: translateY(0);
  }
`;

const LoadingSpinner = styled.div`
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid #ffffff;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s ease-in-out infinite;
  margin-right: 10px;
  
  @keyframes spin {
    to { transform: rotate(360deg); }
  }
`;

const PresetButtons = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 15px;
`;

const PresetButton = styled.button`
  padding: 6px 12px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 20px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: #667eea;
    color: white;
    border-color: #667eea;
  }
`;

function ImageGenerator({ onGenerate, isLoading }) {
  const [prompt, setPrompt] = useState('');
  const [settings, setSettings] = useState({
    width: 512,
    height: 512,
    steps: 20,
    cfg_scale: 7,
    batch_size: 1,
    seed: -1
  });

  const presetPrompts = [
    '像素风格战士角色',
    '8位游戏怪物',
    '像素艺术宝箱',
    '复古游戏道具',
    '像素风景背景',
    '16位角色精灵'
  ];

  const handleSubmit = (e) => {
    e.preventDefault();
    if (prompt.trim() && !isLoading) {
      const enhancedPrompt = `pixel art, 8-bit style, game sprite, ${prompt}, clean background, high contrast, sharp pixels`;
      onGenerate(enhancedPrompt, settings);
    }
  };

  const handleSettingChange = (key, value) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const addPresetPrompt = (preset) => {
    setPrompt(prev => prev ? `${prev}, ${preset}` : preset);
  };

  return (
    <GeneratorContainer>
      <Title>
        ✨ 图像生成
      </Title>
      
      <form onSubmit={handleSubmit}>
        <FormGroup>
          <Label>描述文字</Label>
          <PresetButtons>
            {presetPrompts.map(preset => (
              <PresetButton 
                key={preset}
                type="button"
                onClick={() => addPresetPrompt(preset)}
              >
                {preset}
              </PresetButton>
            ))}
          </PresetButtons>
          <TextArea
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            placeholder="描述你想要生成的像素游戏角色或物品，例如：勇敢的骑士，手持宝剑，蓝色盔甲..."
            required
          />
        </FormGroup>

        <SettingsGrid>
          <FormGroup>
            <Label>宽度</Label>
            <Input
              type="number"
              value={settings.width}
              onChange={(e) => handleSettingChange('width', parseInt(e.target.value))}
              min="64"
              max="1024"
              step="64"
            />
          </FormGroup>
          
          <FormGroup>
            <Label>高度</Label>
            <Input
              type="number"
              value={settings.height}
              onChange={(e) => handleSettingChange('height', parseInt(e.target.value))}
              min="64"
              max="1024"
              step="64"
            />
          </FormGroup>
          
          <FormGroup>
            <Label>生成步数</Label>
            <Input
              type="number"
              value={settings.steps}
              onChange={(e) => handleSettingChange('steps', parseInt(e.target.value))}
              min="10"
              max="50"
            />
          </FormGroup>
          
          <FormGroup>
            <Label>CFG Scale</Label>
            <Input
              type="number"
              value={settings.cfg_scale}
              onChange={(e) => handleSettingChange('cfg_scale', parseFloat(e.target.value))}
              min="1"
              max="20"
              step="0.5"
            />
          </FormGroup>
        </SettingsGrid>

        <GenerateButton type="submit" disabled={isLoading || !prompt.trim()}>
          {isLoading && <LoadingSpinner />}
          {isLoading ? '生成中...' : '🎨 生成图像'}
        </GenerateButton>
      </form>
    </GeneratorContainer>
  );
}

export default ImageGenerator;
