# 像素游戏图像生成器 - Docker 构建脚本
# PowerShell 脚本用于构建和管理 Docker 镜像

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("build", "start", "stop", "restart", "clean", "logs", "status")]
    [string]$Action = "build",
    
    [Parameter(Mandatory=$false)]
    [switch]$NoBuild,
    
    [Parameter(Mandatory=$false)]
    [switch]$Force,
    
    [Parameter(Mandatory=$false)]
    [string]$Service = ""
)

# 颜色输出函数
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Write-Success {
    param([string]$Message)
    Write-ColorOutput "✅ $Message" "Green"
}

function Write-Error {
    param([string]$Message)
    Write-ColorOutput "❌ $Message" "Red"
}

function Write-Info {
    param([string]$Message)
    Write-ColorOutput "ℹ️  $Message" "Cyan"
}

function Write-Warning {
    param([string]$Message)
    Write-ColorOutput "⚠️  $Message" "Yellow"
}

# 检查 Docker 是否安装
function Test-DockerInstalled {
    try {
        $null = docker --version
        return $true
    }
    catch {
        Write-Error "Docker 未安装或未在 PATH 中找到"
        Write-Info "请安装 Docker Desktop: https://www.docker.com/products/docker-desktop"
        return $false
    }
}

# 检查 Docker Compose 是否可用
function Test-DockerComposeAvailable {
    try {
        $null = docker compose version
        return $true
    }
    catch {
        Write-Error "Docker Compose 不可用"
        Write-Info "请确保 Docker Desktop 已启动"
        return $false
    }
}

# 构建镜像
function Build-Images {
    Write-Info "开始构建 Docker 镜像..."
    
    if ($Service -eq "backend" -or $Service -eq "") {
        Write-Info "构建后端镜像..."
        docker build -t pixel-game-backend:latest ./backend
        if ($LASTEXITCODE -ne 0) {
            Write-Error "后端镜像构建失败"
            return $false
        }
        Write-Success "后端镜像构建完成"
    }
    
    if ($Service -eq "frontend" -or $Service -eq "") {
        Write-Info "构建前端镜像..."
        docker build -t pixel-game-frontend:latest ./frontend
        if ($LASTEXITCODE -ne 0) {
            Write-Error "前端镜像构建失败"
            return $false
        }
        Write-Success "前端镜像构建完成"
    }
    
    Write-Success "所有镜像构建完成"
    return $true
}

# 启动服务
function Start-Services {
    Write-Info "启动像素游戏图像生成器服务..."
    
    if (-not $NoBuild) {
        if (-not (Build-Images)) {
            return $false
        }
    }
    
    if ($Service -ne "") {
        docker compose up -d $Service
    } else {
        docker compose up -d
    }
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "服务启动成功"
        Write-Info "前端访问地址: http://localhost"
        Write-Info "后端API地址: http://localhost:8000"
        Write-Info "API文档地址: http://localhost:8000/docs"
        return $true
    } else {
        Write-Error "服务启动失败"
        return $false
    }
}

# 停止服务
function Stop-Services {
    Write-Info "停止服务..."
    
    if ($Service -ne "") {
        docker compose stop $Service
    } else {
        docker compose down
    }
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "服务已停止"
    } else {
        Write-Error "停止服务时出错"
    }
}

# 重启服务
function Restart-Services {
    Write-Info "重启服务..."
    Stop-Services
    Start-Sleep -Seconds 2
    Start-Services
}

# 清理资源
function Clean-Resources {
    Write-Warning "这将删除所有相关的 Docker 镜像和容器"
    
    if (-not $Force) {
        $confirm = Read-Host "确定要继续吗? (y/N)"
        if ($confirm -ne "y" -and $confirm -ne "Y") {
            Write-Info "操作已取消"
            return
        }
    }
    
    Write-Info "停止并删除容器..."
    docker compose down --remove-orphans
    
    Write-Info "删除镜像..."
    docker rmi pixel-game-backend:latest -f 2>$null
    docker rmi pixel-game-frontend:latest -f 2>$null
    
    Write-Info "清理未使用的资源..."
    docker system prune -f
    
    Write-Success "清理完成"
}

# 查看日志
function Show-Logs {
    if ($Service -ne "") {
        docker compose logs -f $Service
    } else {
        docker compose logs -f
    }
}

# 查看状态
function Show-Status {
    Write-Info "服务状态:"
    docker compose ps
    
    Write-Info "`n镜像信息:"
    docker images | Select-String "pixel-game"
    
    Write-Info "`n磁盘使用情况:"
    docker system df
}

# 主函数
function Main {
    Write-ColorOutput "🎮 像素游戏图像生成器 - Docker 管理脚本" "Magenta"
    Write-ColorOutput "================================================" "Magenta"
    
    # 检查 Docker 环境
    if (-not (Test-DockerInstalled)) {
        exit 1
    }
    
    if (-not (Test-DockerComposeAvailable)) {
        exit 1
    }
    
    # 执行相应操作
    switch ($Action.ToLower()) {
        "build" {
            if (Build-Images) {
                Write-Success "构建完成！使用 './build.ps1 start' 启动服务"
            } else {
                exit 1
            }
        }
        "start" {
            if (-not (Start-Services)) {
                exit 1
            }
        }
        "stop" {
            Stop-Services
        }
        "restart" {
            Restart-Services
        }
        "clean" {
            Clean-Resources
        }
        "logs" {
            Show-Logs
        }
        "status" {
            Show-Status
        }
        default {
            Write-Error "未知操作: $Action"
            Write-Info "可用操作: build, start, stop, restart, clean, logs, status"
            exit 1
        }
    }
}

# 显示帮助信息
if ($args -contains "-h" -or $args -contains "--help") {
    Write-ColorOutput "🎮 像素游戏图像生成器 - Docker 管理脚本" "Magenta"
    Write-ColorOutput "================================================" "Magenta"
    Write-Host ""
    Write-Host "用法: ./build.ps1 [操作] [选项]"
    Write-Host ""
    Write-Host "操作:"
    Write-Host "  build     构建 Docker 镜像"
    Write-Host "  start     启动服务"
    Write-Host "  stop      停止服务"
    Write-Host "  restart   重启服务"
    Write-Host "  clean     清理所有资源"
    Write-Host "  logs      查看日志"
    Write-Host "  status    查看状态"
    Write-Host ""
    Write-Host "选项:"
    Write-Host "  -NoBuild     启动时不重新构建镜像"
    Write-Host "  -Force       强制执行，不询问确认"
    Write-Host "  -Service     指定特定服务 (backend/frontend)"
    Write-Host ""
    Write-Host "示例:"
    Write-Host "  ./build.ps1 build                    # 构建所有镜像"
    Write-Host "  ./build.ps1 start                    # 构建并启动所有服务"
    Write-Host "  ./build.ps1 start -NoBuild           # 启动服务但不重新构建"
    Write-Host "  ./build.ps1 logs -Service backend    # 查看后端日志"
    Write-Host "  ./build.ps1 clean -Force             # 强制清理所有资源"
    exit 0
}

# 执行主函数
Main
