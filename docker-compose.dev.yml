# 开发环境 Docker Compose 配置
version: '3.8'

services:
  # 后端服务 (开发模式)
  backend:
    image: pixel-game-backend:latest
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: pixel-game-backend-dev
    ports:
      - "8000:8000"
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]
    volumes:
      - ./models:/app/models
      - ./outputs:/app/outputs
      - ./backend:/app  # 挂载源代码以支持热重载
    environment:
      - PYTHONPATH=/app
      - HF_HOME=/app/models/huggingface
      - PYTHONUNBUFFERED=1
      - DEVELOPMENT=1
    restart: unless-stopped
    networks:
      - pixel-game-network
    command: ["python", "-m", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]

  # 前端服务 (开发模式)
  frontend:
    image: node:18-alpine
    container_name: pixel-game-frontend-dev
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules  # 匿名卷以避免覆盖 node_modules
    working_dir: /app
    environment:
      - CHOKIDAR_USEPOLLING=true
      - REACT_APP_API_URL=http://localhost:8000
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - pixel-game-network
    command: ["sh", "-c", "npm install && npm start"]

# 网络配置
networks:
  pixel-game-network:
    driver: bridge

# 数据卷配置
volumes:
  models-data:
  outputs-data:
