import React, { useState } from 'react';
import styled from 'styled-components';
import axios from 'axios';

const GeneratorContainer = styled.div`
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
`;

const Title = styled.h3`
  margin: 0 0 20px 0;
  color: #333;
  font-size: 1.3rem;
  display: flex;
  align-items: center;
  gap: 10px;
`;

const SettingsGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-bottom: 20px;
`;

const FormGroup = styled.div`
  margin-bottom: 15px;
`;

const Label = styled.label`
  display: block;
  margin-bottom: 8px;
  color: #555;
  font-weight: 500;
  font-size: 0.9rem;
`;

const Input = styled.input`
  width: 100%;
  padding: 10px 12px;
  border: 2px solid #e1e5e9;
  border-radius: 6px;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  
  &:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  }
`;

const Select = styled.select`
  width: 100%;
  padding: 10px 12px;
  border: 2px solid #e1e5e9;
  border-radius: 6px;
  font-size: 0.9rem;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  }
`;

const GenerateButton = styled.button`
  width: 100%;
  padding: 15px;
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 20px;
  
  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
  }
  
  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
  }
`;

const PreviewArea = styled.div`
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  min-height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
`;

const SpriteSheetImage = styled.img`
  max-width: 100%;
  max-height: 300px;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  image-rendering: pixelated;
  image-rendering: -moz-crisp-edges;
  image-rendering: crisp-edges;
`;

const DownloadButton = styled.button`
  background: #667eea;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 10px 20px;
  margin-top: 15px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s ease;
  
  &:hover {
    background: #5a6fd8;
    transform: translateY(-1px);
  }
`;

const InfoText = styled.p`
  color: #666;
  font-size: 0.9rem;
  margin: 10px 0;
  line-height: 1.4;
`;

const LoadingSpinner = styled.div`
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid #ffffff;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s ease-in-out infinite;
  margin-right: 10px;
  
  @keyframes spin {
    to { transform: rotate(360deg); }
  }
`;

function SpriteSheetGenerator({ images }) {
  const [settings, setSettings] = useState({
    columns: 4,
    rows: 0, // 0 表示自动计算
    spacing: 2,
    background: 'transparent',
    format: 'png'
  });
  const [spriteSheetUrl, setSpriteSheetUrl] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);

  const handleSettingChange = (key, value) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const generateSpriteSheet = async () => {
    if (images.length === 0) {
      alert('请先生成一些图像');
      return;
    }

    setIsGenerating(true);
    try {
      const response = await axios.post('/api/generate-sprite-sheet', {
        images: images,
        settings: settings
      });
      
      setSpriteSheetUrl(response.data.sprite_sheet_url);
    } catch (error) {
      console.error('雪碧图生成失败:', error);
      alert('雪碧图生成失败，请重试');
    } finally {
      setIsGenerating(false);
    }
  };

  const downloadSpriteSheet = () => {
    if (spriteSheetUrl) {
      const link = document.createElement('a');
      link.href = spriteSheetUrl;
      link.download = `sprite-sheet-${Date.now()}.${settings.format}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const calculatedRows = settings.rows || Math.ceil(images.length / settings.columns);

  return (
    <GeneratorContainer>
      <Title>
        🧩 雪碧图生成器
      </Title>
      
      <SettingsGrid>
        <FormGroup>
          <Label>列数</Label>
          <Input
            type="number"
            value={settings.columns}
            onChange={(e) => handleSettingChange('columns', parseInt(e.target.value))}
            min="1"
            max="10"
          />
        </FormGroup>
        
        <FormGroup>
          <Label>行数 (0=自动)</Label>
          <Input
            type="number"
            value={settings.rows}
            onChange={(e) => handleSettingChange('rows', parseInt(e.target.value))}
            min="0"
            max="10"
          />
        </FormGroup>
        
        <FormGroup>
          <Label>间距 (像素)</Label>
          <Input
            type="number"
            value={settings.spacing}
            onChange={(e) => handleSettingChange('spacing', parseInt(e.target.value))}
            min="0"
            max="20"
          />
        </FormGroup>
        
        <FormGroup>
          <Label>背景</Label>
          <Select
            value={settings.background}
            onChange={(e) => handleSettingChange('background', e.target.value)}
          >
            <option value="transparent">透明</option>
            <option value="white">白色</option>
            <option value="black">黑色</option>
            <option value="gray">灰色</option>
          </Select>
        </FormGroup>
      </SettingsGrid>

      <InfoText>
        将生成 {settings.columns} × {calculatedRows} 的雪碧图，包含 {images.length} 张图像
      </InfoText>

      <GenerateButton 
        onClick={generateSpriteSheet} 
        disabled={isGenerating || images.length === 0}
      >
        {isGenerating && <LoadingSpinner />}
        {isGenerating ? '生成中...' : '🧩 生成雪碧图'}
      </GenerateButton>

      <PreviewArea>
        {spriteSheetUrl ? (
          <div>
            <SpriteSheetImage src={spriteSheetUrl} alt="Generated Sprite Sheet" />
            <DownloadButton onClick={downloadSpriteSheet}>
              💾 下载雪碧图
            </DownloadButton>
          </div>
        ) : (
          <div>
            <div style={{ fontSize: '3rem', marginBottom: '15px', opacity: 0.3 }}>
              🧩
            </div>
            <InfoText>
              雪碧图预览区域<br/>
              生成后将在这里显示
            </InfoText>
          </div>
        )}
      </PreviewArea>
    </GeneratorContainer>
  );
}

export default SpriteSheetGenerator;
