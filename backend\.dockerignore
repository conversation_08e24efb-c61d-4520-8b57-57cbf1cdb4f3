# 后端 .dockerignore

# Python 缓存
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# 虚拟环境
venv/
env/
ENV/

# IDE 文件
.vscode/
.idea/
*.swp
*.swo

# 操作系统文件
.DS_Store
Thumbs.db

# Git
.git/
.gitignore

# 测试和覆盖率
.coverage
.pytest_cache/
htmlcov/

# 环境变量文件
.env
.env.local
.env.development
.env.test
.env.production

# 日志文件
*.log

# 模型文件（太大，应该通过挂载卷提供）
models/
outputs/

# Jupyter Notebook
.ipynb_checkpoints

# 其他
.cache/
.mypy_cache/
.dmypy.json
dmypy.json
