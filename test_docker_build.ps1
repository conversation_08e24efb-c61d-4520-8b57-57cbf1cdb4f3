# Docker 构建测试脚本
# 用于验证前后端分离的 Docker 架构

param(
    [switch]$SkipBuild,
    [switch]$Verbose
)

function Write-TestResult {
    param(
        [string]$TestName,
        [bool]$Success,
        [string]$Details = ""
    )
    
    if ($Success) {
        Write-Host "✅ $TestName" -ForegroundColor Green
    } else {
        Write-Host "❌ $TestName" -ForegroundColor Red
    }
    
    if ($Details -and $Verbose) {
        Write-Host "   $Details" -ForegroundColor Gray
    }
}

function Test-DockerEnvironment {
    Write-Host "🔍 检查 Docker 环境..." -ForegroundColor Cyan
    
    # 检查 Docker
    try {
        $dockerVersion = docker --version
        Write-TestResult "Docker 已安装" $true $dockerVersion
    } catch {
        Write-TestResult "Docker 已安装" $false "Docker 未找到"
        return $false
    }
    
    # 检查 Docker Compose
    try {
        $composeVersion = docker compose version
        Write-TestResult "Docker Compose 可用" $true $composeVersion
    } catch {
        Write-TestResult "Docker Compose 可用" $false "Docker Compose 不可用"
        return $false
    }
    
    return $true
}

function Test-FileStructure {
    Write-Host "`n📁 检查文件结构..." -ForegroundColor Cyan
    
    $requiredFiles = @(
        "frontend/Dockerfile",
        "frontend/package.json",
        "frontend/nginx.conf",
        "backend/Dockerfile", 
        "backend/requirements.txt",
        "backend/main.py",
        "docker-compose.yml",
        "build.ps1"
    )
    
    $allExists = $true
    foreach ($file in $requiredFiles) {
        $exists = Test-Path $file
        Write-TestResult "文件存在: $file" $exists
        if (-not $exists) { $allExists = $false }
    }
    
    return $allExists
}

function Test-DockerBuild {
    Write-Host "`n🔨 测试 Docker 构建..." -ForegroundColor Cyan
    
    if ($SkipBuild) {
        Write-Host "⏭️  跳过构建测试" -ForegroundColor Yellow
        return $true
    }
    
    # 构建后端镜像
    Write-Host "构建后端镜像..." -ForegroundColor Gray
    $backendBuild = docker build -t pixel-game-backend:test ./backend 2>&1
    $backendSuccess = $LASTEXITCODE -eq 0
    Write-TestResult "后端镜像构建" $backendSuccess
    
    if ($Verbose -and -not $backendSuccess) {
        Write-Host $backendBuild -ForegroundColor Red
    }
    
    # 构建前端镜像
    Write-Host "构建前端镜像..." -ForegroundColor Gray
    $frontendBuild = docker build -t pixel-game-frontend:test ./frontend 2>&1
    $frontendSuccess = $LASTEXITCODE -eq 0
    Write-TestResult "前端镜像构建" $frontendSuccess
    
    if ($Verbose -and -not $frontendSuccess) {
        Write-Host $frontendBuild -ForegroundColor Red
    }
    
    return $backendSuccess -and $frontendSuccess
}

function Test-DockerCompose {
    Write-Host "`n🐳 测试 Docker Compose..." -ForegroundColor Cyan
    
    # 验证 docker-compose.yml 语法
    try {
        $composeConfig = docker compose config 2>&1
        $configValid = $LASTEXITCODE -eq 0
        Write-TestResult "docker-compose.yml 语法" $configValid
        
        if ($Verbose -and -not $configValid) {
            Write-Host $composeConfig -ForegroundColor Red
        }
    } catch {
        Write-TestResult "docker-compose.yml 语法" $false "无法验证配置"
        return $false
    }
    
    # 验证开发环境配置
    try {
        $devConfig = docker compose -f docker-compose.dev.yml config 2>&1
        $devConfigValid = $LASTEXITCODE -eq 0
        Write-TestResult "docker-compose.dev.yml 语法" $devConfigValid
        
        if ($Verbose -and -not $devConfigValid) {
            Write-Host $devConfig -ForegroundColor Red
        }
    } catch {
        Write-TestResult "docker-compose.dev.yml 语法" $false "无法验证开发配置"
        return $false
    }
    
    return $configValid -and $devConfigValid
}

function Test-BuildScript {
    Write-Host "`n📜 测试构建脚本..." -ForegroundColor Cyan
    
    # 检查 PowerShell 脚本语法
    try {
        $scriptCheck = powershell -NoProfile -Command "& { . '.\build.ps1' -Action status -WhatIf }" 2>&1
        $scriptValid = $LASTEXITCODE -eq 0
        Write-TestResult "build.ps1 语法" $scriptValid
    } catch {
        Write-TestResult "build.ps1 语法" $false "脚本语法错误"
        return $false
    }
    
    # 检查 Bash 脚本
    if (Get-Command bash -ErrorAction SilentlyContinue) {
        try {
            $bashCheck = bash -n run.sh 2>&1
            $bashValid = $LASTEXITCODE -eq 0
            Write-TestResult "run.sh 语法" $bashValid
        } catch {
            Write-TestResult "run.sh 语法" $false "Bash 脚本语法错误"
            return $false
        }
    } else {
        Write-TestResult "run.sh 语法" $true "Bash 不可用，跳过检查"
    }
    
    return $true
}

function Cleanup-TestImages {
    Write-Host "`n🧹 清理测试镜像..." -ForegroundColor Cyan
    
    docker rmi pixel-game-backend:test -f 2>$null
    docker rmi pixel-game-frontend:test -f 2>$null
    
    Write-Host "测试镜像已清理" -ForegroundColor Green
}

# 主测试流程
function Main {
    Write-Host "🧪 Docker 架构测试" -ForegroundColor Magenta
    Write-Host "==================" -ForegroundColor Magenta
    
    $tests = @(
        @{ Name = "Docker 环境"; Function = { Test-DockerEnvironment } },
        @{ Name = "文件结构"; Function = { Test-FileStructure } },
        @{ Name = "Docker 构建"; Function = { Test-DockerBuild } },
        @{ Name = "Docker Compose"; Function = { Test-DockerCompose } },
        @{ Name = "构建脚本"; Function = { Test-BuildScript } }
    )
    
    $passed = 0
    $total = $tests.Count
    
    foreach ($test in $tests) {
        $result = & $test.Function
        if ($result) {
            $passed++
        }
    }
    
    # 清理
    if (-not $SkipBuild) {
        Cleanup-TestImages
    }
    
    # 结果汇总
    Write-Host "`n📊 测试结果" -ForegroundColor Magenta
    Write-Host "==========" -ForegroundColor Magenta
    Write-Host "通过: $passed/$total" -ForegroundColor $(if ($passed -eq $total) { "Green" } else { "Yellow" })
    
    if ($passed -eq $total) {
        Write-Host "`n🎉 所有测试通过！Docker 架构配置正确。" -ForegroundColor Green
        Write-Host "可以使用以下命令启动应用:" -ForegroundColor Cyan
        Write-Host "  .\build.ps1 start" -ForegroundColor White
        return 0
    } else {
        Write-Host "`n⚠️  部分测试失败，请检查配置。" -ForegroundColor Yellow
        return 1
    }
}

# 执行测试
exit (Main)
