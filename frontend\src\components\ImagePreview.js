import React, { useState } from 'react';
import styled from 'styled-components';

const PreviewContainer = styled.div`
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  min-height: 400px;
`;

const Title = styled.h3`
  margin: 0 0 20px 0;
  color: #333;
  font-size: 1.3rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

const ImageGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 15px;
  max-height: 600px;
  overflow-y: auto;
`;

const ImageCard = styled.div`
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  background: #f8f9fa;
  border: 2px solid ${props => props.selected ? '#667eea' : 'transparent'};
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
`;

const Image = styled.img`
  width: 100%;
  height: 150px;
  object-fit: contain;
  background: white;
  image-rendering: pixelated;
  image-rendering: -moz-crisp-edges;
  image-rendering: crisp-edges;
`;

const ImageOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  
  ${ImageCard}:hover & {
    opacity: 1;
  }
`;

const ActionButton = styled.button`
  background: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  margin: 0 5px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  transition: all 0.2s ease;
  
  &:hover {
    transform: scale(1.1);
    background: #667eea;
    color: white;
  }
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 60px 20px;
  color: #666;
`;

const EmptyIcon = styled.div`
  font-size: 4rem;
  margin-bottom: 20px;
  opacity: 0.3;
`;

const ClearButton = styled.button`
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 15px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: #c82333;
  }
`;

const SelectionInfo = styled.div`
  margin-top: 15px;
  padding: 10px;
  background: #e3f2fd;
  border-radius: 6px;
  font-size: 0.9rem;
  color: #1976d2;
`;

function ImagePreview({ images, onImagesChange }) {
  const [selectedImages, setSelectedImages] = useState(new Set());

  const toggleImageSelection = (index) => {
    const newSelected = new Set(selectedImages);
    if (newSelected.has(index)) {
      newSelected.delete(index);
    } else {
      newSelected.add(index);
    }
    setSelectedImages(newSelected);
  };

  const downloadImage = (imageUrl, index) => {
    const link = document.createElement('a');
    link.href = imageUrl;
    link.download = `pixel-game-sprite-${index + 1}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const deleteImage = (index) => {
    const newImages = images.filter((_, i) => i !== index);
    onImagesChange(newImages);
    
    // 更新选中状态
    const newSelected = new Set();
    selectedImages.forEach(selectedIndex => {
      if (selectedIndex < index) {
        newSelected.add(selectedIndex);
      } else if (selectedIndex > index) {
        newSelected.add(selectedIndex - 1);
      }
    });
    setSelectedImages(newSelected);
  };

  const clearAllImages = () => {
    if (window.confirm('确定要清空所有图像吗？')) {
      onImagesChange([]);
      setSelectedImages(new Set());
    }
  };

  const selectAll = () => {
    if (selectedImages.size === images.length) {
      setSelectedImages(new Set());
    } else {
      setSelectedImages(new Set(images.map((_, index) => index)));
    }
  };

  return (
    <PreviewContainer>
      <Title>
        🖼️ 图像预览
        {images.length > 0 && (
          <div>
            <button 
              onClick={selectAll}
              style={{
                background: 'none',
                border: '1px solid #667eea',
                color: '#667eea',
                borderRadius: '4px',
                padding: '5px 10px',
                marginRight: '10px',
                cursor: 'pointer',
                fontSize: '0.8rem'
              }}
            >
              {selectedImages.size === images.length ? '取消全选' : '全选'}
            </button>
            <ClearButton onClick={clearAllImages}>
              清空
            </ClearButton>
          </div>
        )}
      </Title>
      
      {images.length === 0 ? (
        <EmptyState>
          <EmptyIcon>🎨</EmptyIcon>
          <p>还没有生成任何图像</p>
          <p>在左侧输入描述文字开始创作吧！</p>
        </EmptyState>
      ) : (
        <>
          <ImageGrid>
            {images.map((imageUrl, index) => (
              <ImageCard 
                key={index}
                selected={selectedImages.has(index)}
                onClick={() => toggleImageSelection(index)}
              >
                <Image src={imageUrl} alt={`Generated sprite ${index + 1}`} />
                <ImageOverlay>
                  <ActionButton 
                    onClick={(e) => {
                      e.stopPropagation();
                      downloadImage(imageUrl, index);
                    }}
                    title="下载"
                  >
                    💾
                  </ActionButton>
                  <ActionButton 
                    onClick={(e) => {
                      e.stopPropagation();
                      deleteImage(index);
                    }}
                    title="删除"
                  >
                    🗑️
                  </ActionButton>
                </ImageOverlay>
              </ImageCard>
            ))}
          </ImageGrid>
          
          {selectedImages.size > 0 && (
            <SelectionInfo>
              已选择 {selectedImages.size} 张图像，可以用于生成雪碧图
            </SelectionInfo>
          )}
        </>
      )}
    </PreviewContainer>
  );
}

export default ImagePreview;
