# 后端 Dockerfile - 像素游戏图像生成器
FROM docker.1ms.run/python:3.10-slim
RUN echo "Types: deb" > /etc/apt/sources.list.d/tuna.sources && \
    echo "URIs: https://mirrors.tuna.tsinghua.edu.cn/debian" >> /etc/apt/sources.list.d/tuna.sources && \
    echo "Suites: bookworm bookworm-updates bookworm-backports" >> /etc/apt/sources.list.d/tuna.sources && \
    echo "Components: main contrib non-free non-free-firmware" >> /etc/apt/sources.list.d/tuna.sources && \
    echo "Signed-By: /usr/share/keyrings/debian-archive-keyring.gpg" >> /etc/apt/sources.list.d/tuna.sources && \
    echo "" >> /etc/apt/sources.list.d/tuna.sources && \
    echo "Types: deb" >> /etc/apt/sources.list.d/tuna.sources && \
    echo "URIs: https://mirrors.tuna.tsinghua.edu.cn/debian-security" >> /etc/apt/sources.list.d/tuna.sources && \
    echo "Suites: bookworm-security" >> /etc/apt/sources.list.d/tuna.sources && \
    echo "Components: main contrib non-free non-free-firmware" >> /etc/apt/sources.list.d/tuna.sources && \
    echo "Signed-By: /usr/share/keyrings/debian-archive-keyring.gpg" >> /etc/apt/sources.list.d/tuna.sources

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    git \
    wget \
    curl \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    libgcc-s1 \
    && rm -rf /var/lib/apt/lists/*
RUN pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple
# 升级 pip
RUN pip install  --upgrade pip

# 复制依赖文件
COPY requirements.txt .

# 安装 Python 依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建必要的目录
RUN mkdir -p /app/outputs
RUN mkdir -p /app/models/huggingface

# 设置环境变量
ENV PYTHONPATH=/app
ENV HF_HOME=/app/models/huggingface
ENV PYTHONUNBUFFERED=1

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8000/api/health || exit 1

# 启动命令
CMD ["python", "main.py"]
