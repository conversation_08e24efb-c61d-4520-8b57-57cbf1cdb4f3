import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import axios from 'axios';
import ImageGenerator from './components/ImageGenerator';
import ModelSelector from './components/ModelSelector';
import ImagePreview from './components/ImagePreview';
import SpriteSheetGenerator from './components/SpriteSheetGenerator';

const AppContainer = styled.div`
  min-height: 100vh;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
`;

const Header = styled.header`
  text-align: center;
  margin-bottom: 30px;
  
  h1 {
    color: white;
    font-size: 2.5rem;
    margin: 0;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
  }
  
  p {
    color: rgba(255,255,255,0.9);
    font-size: 1.1rem;
    margin: 10px 0 0 0;
  }
`;

const MainContent = styled.div`
  max-width: 1400px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 20px;
  }
`;

const LeftPanel = styled.div`
  display: flex;
  flex-direction: column;
  gap: 20px;
`;

const RightPanel = styled.div`
  display: flex;
  flex-direction: column;
  gap: 20px;
`;

function App() {
  const [models, setModels] = useState([]);
  const [selectedModel, setSelectedModel] = useState('');
  const [generatedImages, setGeneratedImages] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    fetchModels();
  }, []);

  const fetchModels = async () => {
    try {
      const response = await axios.get('/api/models');
      setModels(response.data.models);
      if (response.data.models.length > 0) {
        setSelectedModel(response.data.models[0].name);
      }
    } catch (error) {
      console.error('获取模型列表失败:', error);
    }
  };

  const handleImageGeneration = async (prompt, settings) => {
    setIsLoading(true);
    try {
      const response = await axios.post('/api/generate', {
        prompt,
        model: selectedModel,
        ...settings
      });
      
      setGeneratedImages(prev => [...prev, ...response.data.images]);
    } catch (error) {
      console.error('图像生成失败:', error);
      alert('图像生成失败，请检查设置并重试');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AppContainer>
      <Header>
        <h1>🎮 像素游戏图像生成器</h1>
        <p>专业的像素风格雪碧图制作工具</p>
      </Header>
      
      <MainContent>
        <LeftPanel>
          <ModelSelector 
            models={models}
            selectedModel={selectedModel}
            onModelChange={setSelectedModel}
          />
          <ImageGenerator 
            onGenerate={handleImageGeneration}
            isLoading={isLoading}
          />
          <SpriteSheetGenerator 
            images={generatedImages}
          />
        </LeftPanel>
        
        <RightPanel>
          <ImagePreview 
            images={generatedImages}
            onImagesChange={setGeneratedImages}
          />
        </RightPanel>
      </MainContent>
    </AppContainer>
  );
}

export default App;
