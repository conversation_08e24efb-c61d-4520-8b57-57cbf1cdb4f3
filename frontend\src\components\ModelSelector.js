import React from 'react';
import styled from 'styled-components';

const SelectorContainer = styled.div`
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
`;

const Title = styled.h3`
  margin: 0 0 15px 0;
  color: #333;
  font-size: 1.3rem;
  display: flex;
  align-items: center;
  gap: 10px;
`;

const Select = styled.select`
  width: 100%;
  padding: 12px 15px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 1rem;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  }
  
  &:hover {
    border-color: #667eea;
  }
`;

const ModelInfo = styled.div`
  margin-top: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #667eea;
`;

const InfoText = styled.p`
  margin: 0;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.5;
`;

function ModelSelector({ models, selectedModel, onModelChange }) {
  const selectedModelInfo = models.find(model => model.name === selectedModel);

  return (
    <SelectorContainer>
      <Title>
        🤖 选择模型
      </Title>
      
      <Select 
        value={selectedModel} 
        onChange={(e) => onModelChange(e.target.value)}
      >
        {models.map(model => (
          <option key={model.name} value={model.name}>
            {model.display_name || model.name}
          </option>
        ))}
      </Select>
      
      {selectedModelInfo && (
        <ModelInfo>
          <InfoText>
            <strong>模型类型:</strong> {selectedModelInfo.type || '像素风格'}<br/>
            <strong>描述:</strong> {selectedModelInfo.description || '专为像素游戏角色和场景设计的AI模型'}
          </InfoText>
        </ModelInfo>
      )}
    </SelectorContainer>
  );
}

export default ModelSelector;
