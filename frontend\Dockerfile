# 前端 Dockerfile - 像素游戏图像生成器
FROM  docker.1ms.run/node:18-alpine as builder

RUN echo "Types: deb" > /etc/apt/sources.list.d/tuna.sources && \
    echo "URIs: https://mirrors.tuna.tsinghua.edu.cn/debian" >> /etc/apt/sources.list.d/tuna.sources && \
    echo "Suites: bookworm bookworm-updates bookworm-backports" >> /etc/apt/sources.list.d/tuna.sources && \
    echo "Components: main contrib non-free non-free-firmware" >> /etc/apt/sources.list.d/tuna.sources && \
    echo "Signed-By: /usr/share/keyrings/debian-archive-keyring.gpg" >> /etc/apt/sources.list.d/tuna.sources && \
    echo "" >> /etc/apt/sources.list.d/tuna.sources && \
    echo "Types: deb" >> /etc/apt/sources.list.d/tuna.sources && \
    echo "URIs: https://mirrors.tuna.tsinghua.edu.cn/debian-security" >> /etc/apt/sources.list.d/tuna.sources && \
    echo "Suites: bookworm-security" >> /etc/apt/sources.list.d/tuna.sources && \
    echo "Components: main contrib non-free non-free-firmware" >> /etc/apt/sources.list.d/tuna.sources && \
    echo "Signed-By: /usr/share/keyrings/debian-archive-keyring.gpg" >> /etc/apt/sources.list.d/tuna.sources

# 设置工作目录
WORKDIR /app

# 复制 package.json 和 package-lock.json
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 生产阶段 - 使用 nginx 提供静态文件服务
FROM  docker.1ms.run/nginx:alpine

# 复制自定义 nginx 配置
COPY nginx.conf /etc/nginx/nginx.conf

# 复制构建的文件到 nginx 目录
COPY --from=builder /app/build /usr/share/nginx/html

# 暴露端口
EXPOSE 80

# 启动 nginx
CMD ["nginx", "-g", "daemon off;"]
