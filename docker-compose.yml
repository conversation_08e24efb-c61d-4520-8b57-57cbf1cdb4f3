version: '3.8'

services:
  stable-diffusion:
    image: sd-pixel-game-character:latest
    build: .
    container_name: sd-pixel-app
    ports:
      - "7860:7860"
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]
    volumes:
      - ./models:/app/models
      - ./outputs:/app/outputs
    restart: unless-stopped