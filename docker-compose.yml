version: '3.8'

services:
  pixel-game-generator:
    image: pixel-game-sd:latest
    build: .
    container_name: pixel-game-sd-app
    ports:
      - "8000:8000"  # 后端API端口
      - "3000:3000"  # 前端端口 (如果需要开发模式)
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]
    volumes:
      - ./models:/app/models
      - ./outputs:/app/outputs
    environment:
      - PYTHONPATH=/app/backend
      - HF_HOME=/app/models/huggingface
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3