version: '3.8'

services:
  # 后端服务
  backend:
    image: pixel-game-backend:latest
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: pixel-game-backend
    ports:
      - "8000:8000"
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]
    volumes:
      - ./models:/app/models
      - ./outputs:/app/outputs
    environment:
      - PYTHONPATH=/app
      - HF_HOME=/app/models/huggingface
      - PYTHONUNBUFFERED=1
    restart: unless-stopped
    networks:
      - pixel-game-network

  # 前端服务
  frontend:
    image: pixel-game-frontend:latest
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: pixel-game-frontend
    ports:
      - "80:80"
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - pixel-game-network

# 网络配置
networks:
  pixel-game-network:
    driver: bridge

# 数据卷配置
volumes:
  models-data:
  outputs-data: