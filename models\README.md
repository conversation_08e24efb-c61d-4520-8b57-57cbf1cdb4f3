# 模型目录

这个目录用于存放Stable Diffusion模型文件。

## 目录结构

```
models/
├── Stable-diffusion/     # 主要的SD模型文件
├── Lora/                # LoRA模型文件
├── ControlNet/          # ControlNet模型文件
└── huggingface/         # HuggingFace模型缓存
```

## 支持的模型格式

- `.safetensors` (推荐)
- `.ckpt`

## 推荐的像素艺术模型

### 免费模型 (HuggingFace)
1. **runwayml/stable-diffusion-v1-5**
   - 基础模型，适合像素艺术微调
   
2. **stabilityai/stable-diffusion-xl-base-1.0**
   - 高质量基础模型

### 专业像素艺术模型 (需要下载)
1. **Pixel Art Diffusion**
   - 专门训练的像素艺术模型
   - 下载后放入 `Stable-diffusion/` 目录

2. **8-bit Diffusion**
   - 复古游戏风格模型
   - 适合生成8位游戏角色

## 如何添加模型

1. 下载模型文件 (`.safetensors` 或 `.ckpt`)
2. 将文件复制到 `models/Stable-diffusion/` 目录
3. 重启应用，模型会自动被检测到

## 注意事项

- 模型文件通常很大 (2-7GB)，请确保有足够的磁盘空间
- 首次使用HuggingFace模型时会自动下载，需要网络连接
- GPU内存不足时，应用会自动使用CPU模式（速度较慢）

## 模型许可证

请确保你使用的模型符合相应的许可证要求。大多数开源模型允许个人和商业使用，但请查看具体的许可证条款。
