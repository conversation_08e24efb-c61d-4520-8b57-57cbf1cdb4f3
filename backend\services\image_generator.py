import torch
import os
import uuid
import base64
from io import BytesIO
from PIL import Image
from diffusers import StableDiffusionPipeline, DPMSolverMultistepScheduler
from typing import List, Optional
import asyncio
from pathlib import Path

class ImageGenerator:
    """图像生成器 - 使用Stable Diffusion生成像素风格图像"""
    
    def __init__(self):
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.current_pipeline = None
        self.current_model = None
        self.output_dir = Path("outputs")
        self.output_dir.mkdir(exist_ok=True)
        
        print(f"图像生成器初始化完成，使用设备: {self.device}")
    
    def is_gpu_available(self) -> bool:
        """检查GPU是否可用"""
        return torch.cuda.is_available()
    
    async def load_model(self, model_path: str, model_type: str = "checkpoint"):
        """加载指定的模型"""
        if self.current_model == model_path:
            return  # 模型已经加载
            
        print(f"正在加载模型: {model_path}")
        
        try:
            if model_type == "huggingface":
                # 从HuggingFace加载模型
                pipeline = StableDiffusionPipeline.from_pretrained(
                    model_path,
                    torch_dtype=torch.float16 if self.device == "cuda" else torch.float32,
                    safety_checker=None,
                    requires_safety_checker=False
                )
            else:
                # 从本地文件加载模型
                if not os.path.exists(model_path):
                    raise FileNotFoundError(f"模型文件不存在: {model_path}")
                
                pipeline = StableDiffusionPipeline.from_single_file(
                    model_path,
                    torch_dtype=torch.float16 if self.device == "cuda" else torch.float32,
                    safety_checker=None,
                    requires_safety_checker=False
                )
            
            # 优化调度器
            pipeline.scheduler = DPMSolverMultistepScheduler.from_config(
                pipeline.scheduler.config
            )
            
            # 移动到设备
            pipeline = pipeline.to(self.device)
            
            # 启用内存优化
            if self.device == "cuda":
                pipeline.enable_attention_slicing()
                pipeline.enable_model_cpu_offload()
            
            self.current_pipeline = pipeline
            self.current_model = model_path
            
            print(f"模型加载完成: {model_path}")
            
        except Exception as e:
            print(f"模型加载失败: {e}")
            raise
    
    def _enhance_pixel_prompt(self, prompt: str) -> str:
        """增强提示词以生成更好的像素艺术"""
        pixel_keywords = [
            "pixel art",
            "8-bit style",
            "16-bit style", 
            "game sprite",
            "retro game",
            "pixelated",
            "clean pixel art",
            "sharp pixels",
            "no blur",
            "crisp edges",
            "game character",
            "sprite sheet style"
        ]
        
        # 检查是否已经包含像素艺术关键词
        prompt_lower = prompt.lower()
        has_pixel_keywords = any(keyword in prompt_lower for keyword in pixel_keywords[:6])
        
        if not has_pixel_keywords:
            enhanced_prompt = f"pixel art, 8-bit style, game sprite, {prompt}"
        else:
            enhanced_prompt = prompt
            
        # 添加质量和风格关键词
        enhanced_prompt += ", clean background, high contrast, sharp pixels, retro game style, masterpiece, best quality"
        
        # 添加负面提示词
        negative_prompt = "blurry, soft, smooth, realistic, photographic, 3d render, low quality, worst quality, bad anatomy"
        
        return enhanced_prompt, negative_prompt
    
    async def generate(
        self,
        prompt: str,
        model: str,
        width: int = 512,
        height: int = 512,
        steps: int = 20,
        cfg_scale: float = 7.0,
        batch_size: int = 1,
        seed: int = -1
    ) -> List[str]:
        """生成图像"""
        
        # 获取模型管理器实例
        from .model_manager import ModelManager
        model_manager = ModelManager()
        
        # 获取模型信息
        model_info = await model_manager.get_model_info(model)
        if not model_info:
            raise ValueError(f"未找到模型: {model}")
        
        # 加载模型
        await self.load_model(model_info["path"], model_info["type"])
        
        if not self.current_pipeline:
            raise RuntimeError("模型未加载")
        
        # 增强提示词
        enhanced_prompt, negative_prompt = self._enhance_pixel_prompt(prompt)
        
        # 设置随机种子
        if seed == -1:
            seed = torch.randint(0, 2**32 - 1, (1,)).item()
        
        generator = torch.Generator(device=self.device).manual_seed(seed)
        
        print(f"开始生成图像: {enhanced_prompt[:100]}...")
        
        try:
            # 生成图像
            with torch.autocast(self.device):
                result = self.current_pipeline(
                    prompt=enhanced_prompt,
                    negative_prompt=negative_prompt,
                    width=width,
                    height=height,
                    num_inference_steps=steps,
                    guidance_scale=cfg_scale,
                    num_images_per_prompt=batch_size,
                    generator=generator
                )
            
            images = result.images
            image_urls = []
            
            # 保存图像并返回URL
            for i, image in enumerate(images):
                # 应用像素化后处理
                processed_image = self._post_process_pixel_art(image)
                
                # 保存图像
                image_id = str(uuid.uuid4())
                filename = f"pixel_art_{image_id}.png"
                filepath = self.output_dir / filename
                
                processed_image.save(filepath, "PNG")
                
                # 返回相对URL
                image_url = f"/outputs/{filename}"
                image_urls.append(image_url)
                
                print(f"图像已保存: {filepath}")
            
            return image_urls
            
        except Exception as e:
            print(f"图像生成失败: {e}")
            raise
    
    def _post_process_pixel_art(self, image: Image.Image) -> Image.Image:
        """对生成的图像进行像素艺术后处理"""
        # 确保图像是RGB模式
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        # 可选：应用像素化效果
        # 这里可以添加更多的像素艺术后处理逻辑
        # 例如：减少颜色数量、增强对比度等
        
        return image
    
    def _quantize_colors(self, image: Image.Image, num_colors: int = 16) -> Image.Image:
        """减少图像颜色数量以获得更像素化的效果"""
        # 转换为P模式（调色板模式）来减少颜色
        quantized = image.quantize(colors=num_colors, method=Image.MEDIANCUT)
        # 转换回RGB
        return quantized.convert('RGB')
