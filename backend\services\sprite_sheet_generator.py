import uuid
import requests
from PIL import Image, ImageDraw
from pathlib import Path
from typing import List, Dict
import asyncio
from io import BytesIO
import math

class SpriteSheetGenerator:
    """雪碧图生成器 - 将多个图像合并成一个雪碧图"""
    
    def __init__(self):
        self.output_dir = Path("outputs")
        self.output_dir.mkdir(exist_ok=True)
    
    async def create_sprite_sheet(
        self,
        image_urls: List[str],
        settings: Dict
    ) -> str:
        """创建雪碧图"""
        
        if not image_urls:
            raise ValueError("没有提供图像URL")
        
        # 解析设置
        columns = settings.get('columns', 4)
        rows = settings.get('rows', 0)  # 0表示自动计算
        spacing = settings.get('spacing', 2)
        background = settings.get('background', 'transparent')
        format_type = settings.get('format', 'png')
        
        # 如果行数为0，自动计算
        if rows == 0:
            rows = math.ceil(len(image_urls) / columns)
        
        print(f"创建雪碧图: {columns}x{rows}, 间距: {spacing}px, 背景: {background}")
        
        # 下载并加载所有图像
        images = await self._load_images(image_urls)
        
        if not images:
            raise ValueError("无法加载任何图像")
        
        # 计算统一的图像尺寸（使用最大尺寸）
        max_width = max(img.width for img in images)
        max_height = max(img.height for img in images)
        
        # 计算雪碧图总尺寸
        sprite_width = columns * max_width + (columns - 1) * spacing
        sprite_height = rows * max_height + (rows - 1) * spacing
        
        # 创建背景
        sprite_image = self._create_background(sprite_width, sprite_height, background)
        
        # 将图像放置到雪碧图中
        for i, image in enumerate(images):
            if i >= columns * rows:
                break  # 超出网格范围
                
            row = i // columns
            col = i % columns
            
            # 计算位置
            x = col * (max_width + spacing)
            y = row * (max_height + spacing)
            
            # 居中放置图像
            if image.width < max_width or image.height < max_height:
                x += (max_width - image.width) // 2
                y += (max_height - image.height) // 2
            
            # 粘贴图像
            if image.mode == 'RGBA' or 'transparency' in image.info:
                sprite_image.paste(image, (x, y), image)
            else:
                sprite_image.paste(image, (x, y))
        
        # 保存雪碧图
        sprite_id = str(uuid.uuid4())
        filename = f"sprite_sheet_{sprite_id}.{format_type}"
        filepath = self.output_dir / filename
        
        # 根据格式保存
        if format_type.lower() == 'png':
            sprite_image.save(filepath, "PNG", optimize=True)
        elif format_type.lower() in ['jpg', 'jpeg']:
            # JPEG不支持透明度，转换为RGB
            if sprite_image.mode == 'RGBA':
                rgb_image = Image.new('RGB', sprite_image.size, (255, 255, 255))
                rgb_image.paste(sprite_image, mask=sprite_image.split()[-1])
                rgb_image.save(filepath, "JPEG", quality=95, optimize=True)
            else:
                sprite_image.save(filepath, "JPEG", quality=95, optimize=True)
        else:
            sprite_image.save(filepath, format_type.upper())
        
        print(f"雪碧图已保存: {filepath}")
        
        # 返回URL
        return f"/outputs/{filename}"
    
    async def _load_images(self, image_urls: List[str]) -> List[Image.Image]:
        """加载所有图像"""
        images = []
        
        for url in image_urls:
            try:
                image = await self._load_single_image(url)
                if image:
                    images.append(image)
            except Exception as e:
                print(f"加载图像失败 {url}: {e}")
                continue
        
        return images
    
    async def _load_single_image(self, url: str) -> Image.Image:
        """加载单个图像"""
        if url.startswith('/outputs/'):
            # 本地文件
            filepath = self.output_dir / url.replace('/outputs/', '')
            if filepath.exists():
                return Image.open(filepath)
            else:
                raise FileNotFoundError(f"本地文件不存在: {filepath}")
        elif url.startswith('http'):
            # 网络图像
            response = requests.get(url, timeout=10)
            response.raise_for_status()
            return Image.open(BytesIO(response.content))
        else:
            # 尝试作为本地路径
            return Image.open(url)
    
    def _create_background(self, width: int, height: int, background: str) -> Image.Image:
        """创建背景图像"""
        if background == 'transparent':
            return Image.new('RGBA', (width, height), (0, 0, 0, 0))
        elif background == 'white':
            return Image.new('RGB', (width, height), (255, 255, 255))
        elif background == 'black':
            return Image.new('RGB', (width, height), (0, 0, 0))
        elif background == 'gray':
            return Image.new('RGB', (width, height), (128, 128, 128))
        else:
            # 尝试解析为颜色
            try:
                return Image.new('RGB', (width, height), background)
            except:
                # 默认透明背景
                return Image.new('RGBA', (width, height), (0, 0, 0, 0))
    
    def create_animation_frames(
        self,
        sprite_sheet_path: str,
        frame_width: int,
        frame_height: int,
        columns: int,
        rows: int
    ) -> List[Image.Image]:
        """从雪碧图提取动画帧"""
        sprite_image = Image.open(sprite_sheet_path)
        frames = []
        
        for row in range(rows):
            for col in range(columns):
                x = col * frame_width
                y = row * frame_height
                
                frame = sprite_image.crop((
                    x, y,
                    x + frame_width,
                    y + frame_height
                ))
                
                frames.append(frame)
        
        return frames
    
    def create_gif_animation(
        self,
        frames: List[Image.Image],
        duration: int = 100,
        loop: int = 0
    ) -> str:
        """创建GIF动画"""
        if not frames:
            raise ValueError("没有提供动画帧")
        
        # 生成文件名
        gif_id = str(uuid.uuid4())
        filename = f"animation_{gif_id}.gif"
        filepath = self.output_dir / filename
        
        # 保存GIF
        frames[0].save(
            filepath,
            save_all=True,
            append_images=frames[1:],
            duration=duration,
            loop=loop,
            optimize=True
        )
        
        print(f"GIF动画已保存: {filepath}")
        return f"/outputs/{filename}"
