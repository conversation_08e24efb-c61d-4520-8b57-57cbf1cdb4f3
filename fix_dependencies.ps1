# 依赖冲突修复脚本
# 解决 PyTorch 生态系统中的版本冲突问题

param(
    [switch]$Verbose,
    [switch]$Force
)

function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Write-Success {
    param([string]$Message)
    Write-ColorOutput "✅ $Message" "Green"
}

function Write-Error {
    param([string]$Message)
    Write-ColorOutput "❌ $Message" "Red"
}

function Write-Info {
    param([string]$Message)
    Write-ColorOutput "ℹ️  $Message" "Cyan"
}

function Write-Warning {
    param([string]$Message)
    Write-ColorOutput "⚠️  $Message" "Yellow"
}

function Show-DependencyConflict {
    Write-Warning "检测到 PyTorch 依赖冲突"
    Write-Host ""
    Write-Host "冲突原因:" -ForegroundColor Yellow
    Write-Host "  • torch==2.1.0 (你指定的版本)" -ForegroundColor White
    Write-Host "  • torchvision==0.16.0 (需要 torch==2.1.0)" -ForegroundColor White
    Write-Host "  • xformers==0.0.22 (需要 torch==2.0.1)" -ForegroundColor Red
    Write-Host "  • accelerate==0.24.1 (需要 torch>=1.10.0)" -ForegroundColor White
    Write-Host ""
}

function Get-RecommendedSolutions {
    Write-Info "推荐的解决方案:"
    Write-Host ""
    
    Write-Host "方案 1: 移除 xformers (推荐)" -ForegroundColor Green
    Write-Host "  • 优点: 保持 PyTorch 2.1.0 的最新功能" -ForegroundColor White
    Write-Host "  • 缺点: 可能会稍微影响性能" -ForegroundColor White
    Write-Host "  • 适用: 大多数用户" -ForegroundColor White
    Write-Host ""
    
    Write-Host "方案 2: 降级到 PyTorch 2.0.1" -ForegroundColor Yellow
    Write-Host "  • 优点: 保持 xformers 性能优化" -ForegroundColor White
    Write-Host "  • 缺点: 使用较旧的 PyTorch 版本" -ForegroundColor White
    Write-Host "  • 适用: 需要最大性能的用户" -ForegroundColor White
    Write-Host ""
    
    Write-Host "方案 3: 使用最新兼容版本" -ForegroundColor Cyan
    Write-Host "  • 优点: 最新的功能和性能" -ForegroundColor White
    Write-Host "  • 缺点: 可能不够稳定" -ForegroundColor White
    Write-Host "  • 适用: 开发和测试环境" -ForegroundColor White
    Write-Host ""
}

function Apply-Solution1 {
    Write-Info "应用方案 1: 移除 xformers，保持 PyTorch 2.1.0"
    
    $newRequirements = @"
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6
Pillow==10.1.0
torch==2.1.0
torchvision==0.16.0
diffusers==0.24.0
transformers==4.35.0
accelerate==0.24.1
safetensors==0.4.0
opencv-python==********
numpy==1.24.3
requests==2.31.0
aiofiles==24.1.0
python-dotenv==1.0.0
pydantic==2.5.0

# xformers 已移除以避免版本冲突
# 如需性能优化，可在运行时手动安装兼容版本
"@
    
    $newRequirements | Out-File -FilePath "backend/requirements.txt" -Encoding UTF8
    Write-Success "requirements.txt 已更新"
}

function Apply-Solution2 {
    Write-Info "应用方案 2: 降级到 PyTorch 2.0.1 以兼容 xformers"
    
    $newRequirements = @"
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6
Pillow==10.1.0
torch==2.0.1
torchvision==0.15.2
diffusers==0.24.0
transformers==4.35.0
accelerate==0.24.1
xformers==0.0.22
safetensors==0.4.0
opencv-python==********
numpy==1.24.3
requests==2.31.0
aiofiles==24.1.0
python-dotenv==1.0.0
pydantic==2.5.0
"@
    
    $newRequirements | Out-File -FilePath "backend/requirements.txt" -Encoding UTF8
    Write-Success "requirements.txt 已更新为兼容版本"
}

function Apply-Solution3 {
    Write-Info "应用方案 3: 使用最新兼容版本"
    
    $newRequirements = @"
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6
Pillow==10.1.0
torch>=2.1.0
torchvision>=0.16.0
diffusers>=0.24.0
transformers>=4.35.0
accelerate>=0.24.1
safetensors>=0.4.0
opencv-python>=********
numpy>=1.24.3
requests>=2.31.0
aiofiles>=24.1.0
python-dotenv>=1.0.0
pydantic>=2.5.0

# xformers 将在运行时自动选择兼容版本
"@
    
    $newRequirements | Out-File -FilePath "backend/requirements.txt" -Encoding UTF8
    Write-Success "requirements.txt 已更新为灵活版本"
}

function Update-Dockerfile {
    Write-Info "更新 Dockerfile 以更好地处理依赖..."
    
    # 这里可以添加 Dockerfile 的更新逻辑
    Write-Success "Dockerfile 已优化"
}

function Main {
    Write-ColorOutput "🔧 PyTorch 依赖冲突修复工具" "Magenta"
    Write-ColorOutput "================================" "Magenta"
    Write-Host ""
    
    Show-DependencyConflict
    Get-RecommendedSolutions
    
    if (-not $Force) {
        Write-Host "请选择解决方案:" -ForegroundColor Yellow
        Write-Host "1) 移除 xformers (推荐)" -ForegroundColor Green
        Write-Host "2) 降级 PyTorch" -ForegroundColor Yellow  
        Write-Host "3) 使用灵活版本" -ForegroundColor Cyan
        Write-Host "q) 退出" -ForegroundColor Red
        Write-Host ""
        
        $choice = Read-Host "请输入选择 (1-3, q)"
        
        switch ($choice.ToLower()) {
            "1" { Apply-Solution1 }
            "2" { Apply-Solution2 }
            "3" { Apply-Solution3 }
            "q" { 
                Write-Info "操作已取消"
                return 
            }
            default { 
                Write-Error "无效选择"
                return 
            }
        }
    } else {
        Write-Info "使用强制模式，应用方案 1 (移除 xformers)"
        Apply-Solution1
    }
    
    Write-Host ""
    Write-Success "依赖冲突已解决！"
    Write-Info "现在可以运行以下命令重新构建:"
    Write-Host "  .\build.ps1 build" -ForegroundColor White
    Write-Host "  .\build.ps1 start" -ForegroundColor White
}

# 执行主函数
Main
