# 🎮 像素游戏图像生成器

专门为像素游戏制作图像的 Stable Diffusion 应用，支持文字生成雪碧图、预览和模型管理。

## ✨ 功能特性

- 🎨 **文字生成图像**: 输入描述文字，生成像素风格的游戏角色和物品
- 🧩 **雪碧图制作**: 将多个生成的图像合并成游戏开发所需的雪碧图
- 👀 **实时预览**: 即时预览生成的图像，支持下载和删除
- 🤖 **模型管理**: 从目录自动读取和管理不同的像素艺术模型
- 🐳 **Docker部署**: 完整的Docker化解决方案，一键启动

## 🚀 快速开始

### 前置要求

- Docker 和 Docker Compose
- NVIDIA GPU (推荐，用于加速图像生成)
- NVIDIA Container Toolkit (如果使用GPU)

### 启动应用

#### 方法一：使用 PowerShell 脚本 (推荐)
```powershell
# 构建并启动所有服务
.\build.ps1 start

# 或者分步执行
.\build.ps1 build    # 构建镜像
.\build.ps1 start    # 启动服务
```

#### 方法二：使用 Bash 脚本
```bash
# Linux/macOS 用户
chmod +x run.sh
./run.sh
```

#### 方法三：手动使用 Docker Compose
```bash
# 构建并启动
docker compose up --build -d

# 或者分步执行
docker compose build
docker compose up -d
```

#### 准备工作
1. 克隆项目
```bash
git clone <repository-url>
cd aaa-docker
```

2. 准备模型文件 (可选)
```bash
# 将你的Stable Diffusion模型文件放入models目录
mkdir -p models/Stable-diffusion
# 复制 .safetensors 或 .ckpt 文件到 models/Stable-diffusion/
```

3. 访问应用
- 前端界面: http://localhost (端口 80)
- 后端API: http://localhost:8000
- API文档: http://localhost:8000/docs

## 📁 项目结构

```
aaa-docker/
├── frontend/                    # React前端应用
│   ├── src/
│   │   ├── components/         # UI组件
│   │   │   ├── ImageGenerator.js
│   │   │   ├── ImagePreview.js
│   │   │   ├── ModelSelector.js
│   │   │   └── SpriteSheetGenerator.js
│   │   └── App.js
│   ├── Dockerfile              # 前端Docker构建文件
│   ├── nginx.conf              # Nginx配置文件
│   ├── package.json
│   └── .dockerignore
├── backend/                     # FastAPI后端服务
│   ├── services/               # 核心服务
│   │   ├── model_manager.py
│   │   ├── image_generator.py
│   │   └── sprite_sheet_generator.py
│   ├── main.py                # API入口
│   ├── requirements.txt
│   ├── Dockerfile             # 后端Docker构建文件
│   └── .dockerignore
├── models/                     # 模型文件目录
│   ├── Stable-diffusion/      # SD模型存放位置
│   └── README.md              # 模型使用说明
├── outputs/                    # 生成的图像输出
├── docker-compose.yml         # 生产环境Docker编排
├── docker-compose.dev.yml     # 开发环境Docker编排
├── build.ps1                  # PowerShell构建脚本
├── run.sh                     # Bash启动脚本
└── test_app.py               # 应用测试脚本
```

## 🎯 使用指南

### 1. 选择模型
- 应用会自动扫描 `models/Stable-diffusion/` 目录中的模型文件
- 支持 `.safetensors` 和 `.ckpt` 格式
- 如果没有本地模型，会使用默认的HuggingFace模型

### 2. 生成图像
- 在文字输入框中描述你想要的像素游戏元素
- 可以使用预设的提示词快速开始
- 调整生成参数（尺寸、步数、CFG Scale等）
- 点击"生成图像"按钮

### 3. 制作雪碧图
- 生成多个图像后，在右侧预览区域选择需要的图像
- 在雪碧图生成器中设置布局参数（行数、列数、间距等）
- 点击"生成雪碧图"创建最终的游戏资源

### 4. 下载和管理
- 单击图像可以选择/取消选择
- 悬停在图像上显示下载和删除按钮
- 雪碧图生成后可以直接下载

## ⚙️ 配置说明

### 环境变量
- `PYTHONPATH`: Python模块路径
- `HF_HOME`: HuggingFace模型缓存目录

### 端口配置
- `8000`: 后端API服务端口
- `3000`: 前端开发服务端口（可选）

### GPU支持
应用会自动检测并使用可用的NVIDIA GPU。如果没有GPU，会回退到CPU模式（速度较慢）。

## 🔧 开发模式

### 使用开发环境 Docker Compose
```bash
# 启动开发环境（支持热重载）
docker compose -f docker-compose.dev.yml up --build

# 前端开发服务器: http://localhost:3000
# 后端API服务器: http://localhost:8000
```

### 本地开发
如果需要在本地直接运行：

#### 后端开发
```bash
cd backend
pip install -r requirements.txt
python main.py
```

#### 前端开发
```bash
cd frontend
npm install
npm start
```

### PowerShell 脚本管理
```powershell
# 查看所有可用命令
.\build.ps1 --help

# 构建特定服务
.\build.ps1 build -Service backend
.\build.ps1 build -Service frontend

# 查看服务日志
.\build.ps1 logs
.\build.ps1 logs -Service backend

# 查看服务状态
.\build.ps1 status

# 清理所有资源
.\build.ps1 clean -Force
```

## 📝 API文档

启动应用后，访问 http://localhost:8000/docs 查看完整的API文档。

主要API端点：
- `GET /api/models` - 获取可用模型列表
- `POST /api/generate` - 生成图像
- `POST /api/generate-sprite-sheet` - 生成雪碧图
- `GET /api/health` - 健康检查

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

MIT License