#!/usr/bin/env python3
"""
测试脚本 - 验证像素游戏图像生成器的基本功能
"""

import requests
import time
import json

def test_api_health():
    """测试API健康状态"""
    try:
        response = requests.get("http://localhost:8000/api/health", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("✅ API健康检查通过")
            print(f"   状态: {data.get('status')}")
            print(f"   已加载模型数: {data.get('models_loaded')}")
            print(f"   GPU可用: {data.get('gpu_available')}")
            return True
        else:
            print(f"❌ API健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API连接失败: {e}")
        return False

def test_models_endpoint():
    """测试模型列表接口"""
    try:
        response = requests.get("http://localhost:8000/api/models", timeout=10)
        if response.status_code == 200:
            data = response.json()
            models = data.get('models', [])
            print(f"✅ 模型列表获取成功，共 {len(models)} 个模型")
            for model in models:
                print(f"   - {model.get('display_name')} ({model.get('name')})")
            return True
        else:
            print(f"❌ 模型列表获取失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 模型列表请求失败: {e}")
        return False

def test_frontend():
    """测试前端页面"""
    try:
        response = requests.get("http://localhost:8000/", timeout=10)
        if response.status_code == 200:
            print("✅ 前端页面访问成功")
            return True
        else:
            print(f"❌ 前端页面访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 前端页面请求失败: {e}")
        return False

def test_image_generation():
    """测试图像生成功能"""
    try:
        # 获取可用模型
        models_response = requests.get("http://localhost:8000/api/models", timeout=10)
        if models_response.status_code != 200:
            print("❌ 无法获取模型列表，跳过图像生成测试")
            return False
        
        models = models_response.json().get('models', [])
        if not models:
            print("❌ 没有可用模型，跳过图像生成测试")
            return False
        
        # 使用第一个模型进行测试
        test_model = models[0]['name']
        
        # 发送图像生成请求
        generate_data = {
            "prompt": "pixel art warrior character, 8-bit style",
            "model": test_model,
            "width": 256,
            "height": 256,
            "steps": 10,  # 使用较少步数以加快测试
            "cfg_scale": 7.0,
            "batch_size": 1,
            "seed": 42
        }
        
        print(f"🔄 开始测试图像生成 (模型: {test_model})...")
        response = requests.post(
            "http://localhost:8000/api/generate", 
            json=generate_data,
            timeout=120  # 图像生成可能需要较长时间
        )
        
        if response.status_code == 200:
            data = response.json()
            images = data.get('images', [])
            print(f"✅ 图像生成成功，生成了 {len(images)} 张图像")
            for i, img_url in enumerate(images):
                print(f"   - 图像 {i+1}: {img_url}")
            return True
        else:
            print(f"❌ 图像生成失败: {response.status_code}")
            try:
                error_detail = response.json().get('detail', 'Unknown error')
                print(f"   错误详情: {error_detail}")
            except:
                print(f"   响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 图像生成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试像素游戏图像生成器...")
    print("=" * 50)
    
    # 等待服务启动
    print("⏳ 等待服务启动...")
    time.sleep(5)
    
    tests = [
        ("API健康检查", test_api_health),
        ("前端页面访问", test_frontend),
        ("模型列表接口", test_models_endpoint),
        ("图像生成功能", test_image_generation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 测试: {test_name}")
        if test_func():
            passed += 1
        print("-" * 30)
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！应用运行正常。")
        return 0
    else:
        print("⚠️  部分测试失败，请检查应用配置。")
        return 1

if __name__ == "__main__":
    exit(main())
