# 使用一个已有的Stable Diffusion WebUI基础镜像（请根据实际情况选择或构建基础镜像）
FROM docker.1ms.run/stable-diffusion-webui:base

# 设置工作目录
WORKDIR /app

# 安装系统依赖（确保包含Python、Git等）
RUN apt-get update && apt-get install -y \
    python3.10 \
    python3-pip \
    git \
    libgl1 \
    libglib2.0-0 \
    && rm -rf /var/lib/apt/lists/*

# 复制必要的模型配置文件（需提前准备或从指定仓库拉取）
COPY requirements_pixel.txt ./
RUN pip install -r requirements_pixel.txt

# 创建存放模型的目录
RUN mkdir -p /app/models/Stable-diffusion /app/models/Lora /app/models/ControlNet

# 下载或复制像素风格模型（此处以示例模型链接示意，实际需替换为有效链接或通过COPY指令添加）
# 注意：模型文件较大，建议在构建前自行下载到本地目录，使用COPY指令复制，或使用wget/curl下载已知的公开模型
# COPY ./models/ /app/models/

# 下载一些推荐的像素风格Lora模型（例如 MG_像素风格）
# RUN wget -O /app/models/Lora/MG_Pixel_Style.safetensors https://example.com/path/to/MG_pixel_style.safetensors

# 暴露端口（WebUI默认端口）
EXPOSE 7860

# 设置启动脚本
COPY run.sh .
RUN chmod +x run.sh

CMD ["./run.sh"]