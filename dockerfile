# 使用Python 3.10基础镜像
FROM python:3.10-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    git \
    wget \
    curl \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    && rm -rf /var/lib/apt/lists/*

# 安装Node.js (用于前端构建)
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash - \
    && apt-get install -y nodejs

# 复制后端依赖文件并安装
COPY backend/requirements.txt ./backend/
RUN pip install --no-cache-dir -r backend/requirements.txt

# 复制前端文件并构建
COPY frontend/ ./frontend/
WORKDIR /app/frontend
RUN npm install && npm run build

# 回到主工作目录
WORKDIR /app

# 复制后端代码
COPY backend/ ./backend/

# 创建必要的目录
RUN mkdir -p /app/models/Stable-diffusion /app/models/Lora /app/models/ControlNet
RUN mkdir -p /app/outputs

# 暴露端口
EXPOSE 8000 3000

# 设置启动脚本
COPY run.sh .
RUN chmod +x run.sh

CMD ["./run.sh"]