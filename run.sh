#!/bin/bash

echo "🚀 启动像素游戏图像生成器..."

# 检查 Docker 和 Docker Compose 是否可用
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装 Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    echo "❌ Docker Compose 不可用，请确保 Docker Desktop 已启动"
    exit 1
fi

# 构建并启动服务
echo "📦 构建 Docker 镜像..."
docker compose build

echo "🚀 启动服务..."
docker compose up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "📊 检查服务状态..."
docker compose ps

echo ""
echo "✅ 服务启动完成！"
echo "🌐 前端访问地址: http://localhost"
echo "🔧 后端API地址: http://localhost:8000"
echo "📚 API文档地址: http://localhost:8000/docs"
echo ""
echo "💡 使用以下命令管理服务:"
echo "   查看日志: docker compose logs -f"
echo "   停止服务: docker compose down"
echo "   重启服务: docker compose restart"
