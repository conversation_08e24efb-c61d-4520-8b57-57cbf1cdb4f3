import os
import json
from pathlib import Path
from typing import List, Dict, Optional
import asyncio

class ModelManager:
    """模型管理器 - 负责加载和管理Stable Diffusion模型"""
    
    def __init__(self, models_dir: str = "/app/models"):
        self.models_dir = Path(models_dir)
        self.loaded_models = {}
        self.available_models = []
        
    async def initialize(self):
        """初始化模型管理器"""
        await self.scan_models()
        
    async def scan_models(self):
        """扫描模型目录，发现可用的模型"""
        self.available_models = []
        
        # 扫描Stable Diffusion模型目录
        sd_models_dir = self.models_dir / "Stable-diffusion"
        if sd_models_dir.exists():
            for model_file in sd_models_dir.glob("*.safetensors"):
                model_info = {
                    "name": model_file.stem,
                    "display_name": self._get_display_name(model_file.stem),
                    "path": str(model_file),
                    "type": "checkpoint",
                    "description": self._get_model_description(model_file.stem)
                }
                self.available_models.append(model_info)
            
            # 也支持.ckpt文件
            for model_file in sd_models_dir.glob("*.ckpt"):
                model_info = {
                    "name": model_file.stem,
                    "display_name": self._get_display_name(model_file.stem),
                    "path": str(model_file),
                    "type": "checkpoint",
                    "description": self._get_model_description(model_file.stem)
                }
                self.available_models.append(model_info)
        
        # 如果没有找到模型，添加默认模型配置
        if not self.available_models:
            self.available_models = [
                {
                    "name": "pixel_art_diffusion",
                    "display_name": "像素艺术扩散模型",
                    "path": "runwayml/stable-diffusion-v1-5",  # 使用HuggingFace模型
                    "type": "huggingface",
                    "description": "基于Stable Diffusion v1.5的像素艺术专用模型"
                },
                {
                    "name": "pixel_sprite_xl",
                    "display_name": "像素精灵XL模型",
                    "path": "stabilityai/stable-diffusion-xl-base-1.0",
                    "type": "huggingface", 
                    "description": "高质量像素游戏角色生成模型"
                }
            ]
            
        print(f"发现 {len(self.available_models)} 个可用模型")
        for model in self.available_models:
            print(f"  - {model['display_name']} ({model['name']})")
    
    def _get_display_name(self, model_name: str) -> str:
        """根据模型文件名生成显示名称"""
        # 移除常见的版本后缀和特殊字符
        display_name = model_name.replace("_", " ").replace("-", " ")
        display_name = display_name.replace("v1", "").replace("v2", "").replace("v3", "")
        display_name = display_name.strip()
        
        # 首字母大写
        return " ".join(word.capitalize() for word in display_name.split())
    
    def _get_model_description(self, model_name: str) -> str:
        """根据模型名称生成描述"""
        name_lower = model_name.lower()
        
        if "pixel" in name_lower:
            return "专为像素艺术风格设计的模型"
        elif "sprite" in name_lower:
            return "游戏精灵和角色生成专用模型"
        elif "8bit" in name_lower or "16bit" in name_lower:
            return "复古游戏风格图像生成模型"
        elif "game" in name_lower:
            return "游戏资源生成专用模型"
        else:
            return "通用图像生成模型，适合像素艺术创作"
    
    async def get_available_models(self) -> List[Dict]:
        """获取所有可用模型的列表"""
        return self.available_models
    
    async def is_model_available(self, model_name: str) -> bool:
        """检查指定模型是否可用"""
        return any(model["name"] == model_name for model in self.available_models)
    
    async def get_model_info(self, model_name: str) -> Optional[Dict]:
        """获取指定模型的详细信息"""
        for model in self.available_models:
            if model["name"] == model_name:
                return model
        return None
    
    async def get_loaded_models(self) -> List[str]:
        """获取已加载的模型列表"""
        return list(self.loaded_models.keys())
    
    def get_model_path(self, model_name: str) -> Optional[str]:
        """获取模型文件路径"""
        for model in self.available_models:
            if model["name"] == model_name:
                return model["path"]
        return None
